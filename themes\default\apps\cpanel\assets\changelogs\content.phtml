<div class="cp-app-container" data-app="changelogs">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                Changelogs
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.4.2 (<time>12 December 2023</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- Fixed reported bugs of the prev version</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.4.1 (<time>14 October 2023</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- Added MoneyPoolCash payment system</p>
                    <p>- Introduced YooKassa payment system for RF (Russian Federation)</p>
                    <p>- Enabled owners of paid profiles to make any post free of charge</p>
                    <p>- Admin now has the ability to disable the content monetization system</p>
                    <p>- Integrated iDrive S3 cloud storage system</p>
                    <p>- Admin can now customize the bank transfer method icon, assign a bank logo, and specify the bank name</p>
                    <p>- Added an SEO banner for site links to ensure proper loading where there is no image</p>
                    <p>- Empowered administrators to appoint moderators with limited rights</p>
                    <p>- Fixed the issue with uploading images in webp format</p>
                    <p>- Admin now have the option to disable user wallet system</p>
                    <p>- Improved the design and display of various elements</p>
                    <p>- Resolved the problem with negative numbers in the user wallet</p>
                    <p>- Introduced Japanese language support</p>
                    <p>- Implemented a commission deducting system for authors when others subscribe to their paid profiles</p>
                    <p>- Added optimization for image quality, adjustable through the admin panel</p>
                    <p>- Enhanced the avatar generation system for new users</p>
                    <p>- Users can now send videos in chats</p>
                    <p>- Addressed user registration system issues</p>
                    <p>- Users can now indicate links to social networks in their profiles</p>
                    <p>- Added CoinPayments system for payments</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.4.0 (<time>20 October 2023</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- Fixed reported bugs of the prev version</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.3.9 (<time>14 October 2023</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- Fixed reported bugs of the prev version</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.3.8 (<time>22 September 2023</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- Added ability to sign up with phone number / with Twilio and Infobip SMS provider service</p>
                    <p>- Fixed issue with dashed email and registration confirmation code (000-000 -> 000000)</p>
                    <p>- Fixed right sidebar hashtags (show more) link issue</p>
                    <p>- Fixed share modal window issue when dark theme is enabled</p>
                    <p>- Fixed problem with remaining days of subscription</p>
                    <p>- Fixed issue with entering last name on the start-up page</p>
                    <p>- And also fixed few minor reported bugs of the prev version</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.3.7 (<time>03 September 2023</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- Fixed reported bugs of the prev version</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.3.6 (<time>31 August 2023</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- Added the ability for users to pin posts at the top of their profile page</p>
                    <p>- Implemented a feature to ban the usage of explicit language in posts through the admin panel</p>
                    <p>- Introduced the ability for admins to pin any post at the top of all user feeds</p>
                    <p>- Enhanced the image display system for posts using smart grids</p>
                    <p>- Enabled voice message functionality within chats</p>
                    <p>- Display the number of people who have voted for a poll in posts</p>
                    <p>- Users can now remove options from polls after creating them</p>
                    <p>- Admins can upload logos and favicons through the admin panel</p>
                    <p>- Users can stop poll voting at any time</p>
                    <p>- Users can specify a budget for their advertisements</p>
                    <p>- Admins can choose users for auto-following, so new users will automatically follow the selected ones</p>
                    <p>- Improved version of the progress bar for post uploads (images, videos, etc.)</p>
                    <p>- Added support for remote storage of media files via Wasabi S3</p>
                    <p>- Integrated Google Vision API's image filtering feature</p>
                    <p>- Introduced content monetization through paid subscriptions</p>
                    <p>- Implemented donation collection system through posts</p>
                    <p>- Added user verification option when purchasing premium accounts</p>
                    <p>- Added an "Accept Request" button in notifications for subscription requests</p>
                    <p>- Fixed the issue related to selecting user country</p>
                    <p>- Admins can view the count of verified users in the admin dashboard</p>
                    <p>- Users can now withdraw funds from their wallets</p>
                    <p>- Users will be notified of incoming transfers from other users</p>
                    <p>- Resolved "Read more" function issue in publication texts</p>
                    <p>- Integrated OneSignal for push notifications</p>
                    <p>- Fixed input problem in messages (shift+enter instead of just enter)</p>
                    <p>- Enhanced visual elements of the UI design system</p>
                    <p>- Improved display of default avatars generated based on usernames</p>
                    <p>- Integrated a new and improved popup notification display system</p>
                    <p>- Enhanced user interaction with UI script components</p>
                    <p>- Added the option to see user online indicators, but users also can disable this feature for their accounts, so no one ca know when they are online</p>
                    <p>- Removed the trending section for performance improvement</p>
                    <p>- Admins can edit static pages through the admin panel</p>
                    <p>- Updated and improved the admin panel, replaced outdated technologies and icons</p>
                    <p>- Added a system to display recommended posts on the homepage, which users can toggle on/off</p>
                    <p>- Introduced the ability to disable certain features in the post box, like images upload system, videos upload system and the rest</p>
                    <p>- Implemented a verification system for purchase codes during installation and updates</p>
                    <p>- Added support for the latest PHP versions (v8.x and above)</p>
                    <p>- Improved design and all script components and interactive elements</p>
                    <p>- Resolved all known issues from previous versions</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.3.7 (<time>03 September 2023</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- Fixed all reported issues in the previous version</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.3.5 (<time>7 January 2023</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Added] the ability to collapse/expand long text</p>
                    <p>- [Added] the ability to show the number of online users in the admin panel</p>
                    <p>- [Added] ability to download audio files</p>
                    <p>- [Added] ability to disable non-binary genders like: Other or They</p>
                    <p>- [Fixed] a bug with the translation of the text on the authorization page</p>
                    <p>- [Fixed] a bug with the premium settings option link on the account settings page</p>
                    <p>- [Fixed] the algorithm for displaying posts so that too old posts are not shown</p>
                    <p>- [Fixed] a bug with login through social networks</p>
                    <p>- [Fixed] issue with duplicate posts on homepage</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.3.4 (<time>28 December 2022</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Added] the ability to replenish the wallet by bank transfer</p>
                    <p>- [Added] the ability to cancel an affiliate withdrawal request</p>
                    <p>- [Added] the ability to transfer money earned through the affiliate program to the user's main wallet</p>
                    <p>- [Added] the ability to disable the API from the admin panel</p>
                    <p>- [Added] the ability to transfer money from a user's wallet to another user</p>
                    <p>- [Added] ability to disable guest page from admin</p>
                    <p>- [Added] placeholder for the video poster if there is no preview</p>
                    <p>- [Added] username restriction system from admin panel</p>
                    <p>- [Added] the ability to connect custom CSS and JS code to the site, for those who want to change something in the display</p>
                    <p>- [Improved] the login page for a new improved layout</p>
                    <p>- [Improved] the input tag of the message entry form to Textarea so that the user can type on a new line</p>
                    <p>- [Added] a new player for videos https://plyr.io/</p>
                    <p>- [Added] the ability to leave the last name field empty, that is, make it optional</p>
                    <p>- [Added] the ability to download videos. This feature can be disabled or enabled by the admin</p>
                    <p>- [Added] a new player to play audio files</p>
                    <p>- [Added] the ability to upload audio files</p>
                    <p>- [Added] the ability to upload document files</p>
                    <p>- [Added] Indian bank RazorPay</p>
                    <p>- [Added] the ability to login through other social networks like Vkontakte, Instagram, Discord, LinkedIn</p>
                    <p>- [Added] a premium system that allows you to disable ads (And many upcoming features that will be released soon) for a paid subscription</p>
                    <p>- [Added] ability for admin to delete user account from their profile page dropdown menu</p>
                    <p>- [Added] ability for admin to block user account from their profile page</p>
                    <p>- [Added] RTL languages support</p>
                    <p>- [Fixed] Bug on swift description with large text</p>
                    <p>- [Fixed] the bug with the translation of the names of months in dates</p>
                    <p>- [Fixed] bugs with sending mail that caused mail clients like Google to mark emails as spam</p>
                    <p>- [Fixed] problem with displaying links from social networks and websites</p>
                    <p>- [Fixed] Google Ads bug with dark theme</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.3.3 (<time>19 May 2022</time>)
                    </h2>
                </div>
                <div class="body">
                    <div class="inline-alertbox-wrapper">
                        <div class="inline-alertbox info">
                            <div class="icon">
                                <?php echo cl_ficon("info"); ?>
                            </div>
                            <div class="alert-message">
                                <h6>
                                    Please pay attention.
                                </h6>
                                <p>
                                    This update is not a planned release due to many requests from customers to fix some bugs.
                                </p>
                                <p>
                                    A planned update with new features will be released as soon as developers complete work on it.
                                </p>
                            </div>
                        </div>
                    </div>

                    <p>- [Fixed] Bug on swift description with large text</p>
                    <p>- [Fixed] Bug on modal windows scrollbar</p>
                    <p>- [Fixed] Bug on affiliate page placeholder</p>
                    <p>- [Fixed] Bug on advertising page placeholder</p>
                    <p>- [Fixed] Bug on post likes display modal window placeholder text</p>
                    <p>- [Fixed] Bug with a verification icon on posts in the mobile browser</p>
                    <p>- [Fixed] Bug with links in messages</p>
                    <p>- [Fixed] Other white minor bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.3.2 (<time>28 March 2022</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Fixed] Previous minor bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.3.1 (<time>25 March 2022</time>)
                    </h2>
                </div>
                <div class="body">
                    <div class="inline-alertbox-wrapper">
                        <div class="inline-alertbox info">
                            <div class="icon">
                                <?php echo cl_ficon("info"); ?>
                            </div>
                            <div class="alert-message">
                                <h6>
                                    Completed global improvisation of the entire script including code and design.
                                </h6>
                                <p>
                                    For a detailed description of this improvement, there is not enough space in the logs, since absolutely all parts of the script have been covered and a deep modernization has been made.<br>

                                    The purpose of this improvisation was to improve the quality of the script to further increase the functionality and stability of the system.<br>

                                    We've spent a lot of time working on improving the core and usability of the script and the quality of the features, and we've reviewed all of our previous bugs and eliminated all ill-conceived code structures in the system.<br>

                                    This work was primarily done for our customers, so that in the future they could independently develop and expand the functionality of the ColibriSM script if they wished, and so that this basis of this script would be a reliable tool for their project, which was not possible to do with previous versions of this script according to our opinion.<br>

                                    As for the many new features that our customers have been asking for and waiting for, they are just around the corner and they will appear in the future days one by one
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <p>- [Suspend] Temporarily suspended RTL support</p>
                    <p>- [Added] additional payment gateways (Stripe, Paystack, AliPay)</p>
                    <p>- [Added] ability to add your city in profile settings</p>
                    <p>- [Added] ability to see is or your follower</p>
                    <p>- [Added] ability see shared followers between two users</p>
                    <p>- [Added] new options to user gender</p>
                    <p>- [Added] ability to disable the swift system from the admin panel</p>
                    <p>- [Added] ability to disable email notifications from the admin panel</p>
                    <p>- [Fixed] bug with storing the selected language</p>
                    <p>- [Fixed] bug with the generation of extra sitemaps</p>
                    <p>- [Fixed] bug with a negative number of followers</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.3.0 (<time>25 Nov 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Fixed] Previous minor bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.2.9 (<time>23 Nov 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Fixed] Previous minor bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.2.8 (<time>22 Nov 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Added] E-mail notifications system</p>
                    <p>- [Added] Google recaptcha</p>
                    <p>- [Added] User verification option for Admin</p>
                    <p>- [Added] User wallet editing option for Admin</p>
                    <p>- [Added] Share to whatsapp option</p>
                    <p>- [Added] Share to telegram option</p>
                    <p>- [Improved] Admin panel</p>
                    <p>- [Improved] Removed unnecessary libraries</p>
                    <p>- [Fixed] Previous minor bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.2.7 (<time>11 Sep 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Added] Option to disable user registration system</p>
                    <p>- [Added] Option to disable the cookie warning window</p>
                    <p>- [Added] Registration of the invited user</p>
                    <p>- [Added] Option to manage UI languages in the admin panel</p>
                    <p>- [Added] Arabic language</p>
                    <p>- [Added] RTL languages support</p>
                    <p>- [Improved] Admin panel</p>
                    <p>- [Fixed] Previous minor bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.2.6 (<time>02 Sep 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Fixed] Previous minor bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.2.5 (<time>01 Sep 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Added] UI display color manager</p>
                    <p>- [Added] Ability to change the position of the currency symbol</p>
                    <p>- [Added] Ability to change the minimum deposit amount</p>
                    <p>- [Added] Mobile app download links in footer</p>
                    <p>- [Added] Possibility to change the minimum amount of the withdrawal request</p>
                    <p>- [Added] New sidebar design</p>
                    <p>- [Fixed] Removed @ from username</p>
                    <p>- [Fixed] Previous minor bugs</p>
                    <p>- [Fixed] Mobile Google Chrome images loading issue</p>
                    <p>- [Improved] Mobile Google Chrome images loading issue</p>
                    <p>
                        The kernel of the system has been improved, unnecessary components have been removed, the loading speed has been increased, a lot of changes have been made to the kernel to further improve the quality of the system and simplify the implementation of new functions
                    </p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.2.4 (<time>29 July 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Fixed] Previous minor bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.2.3 (<time>24 July 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Added] Cookies alert</p>
                    <p>- [Fixed] Removed sidebar movement when scrolling</p>
                    <p>- [Fixed] Previous minor bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.2.2 (<time>06 July 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Fixed] Previous minor bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.2.1 (<time>28 June 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Added] Amazon S3 storage (Admin panel)</p>
                    <p>- [Added] Media upload limit (Admin panel)</p>
                    <p>- [Added] Ability to add multiple admins (Admin panel)</p>
                    <p>- [Added] Header top fix on page scroll</p>
                    <p>- [Added] E-mail verification system</p>
                    <p>- [Added] Username hover popoever</p>
                    <p>- [Added] Voice recorder in the posts section</p>
                    <p>- [Added] Auto detect user language (Guest page)</p>
                    <p>- [Added] API endpoints</p>
                    <p>- [Added] Many improvements in the core of the system</p>
                    <p>- [Fixed] Previous minor bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.2.0 (<time>23 May 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Fixed] Previous minor bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.1.9 (<time>22 May 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Fixed] Previous minor bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.1.8 (<time>21 May 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Fixed] Previous minor bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.1.7 (<time>20 May 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Improved] Emoji system</p>
                    <p>- [Improved] Design</p>
                    <p>- [Added] Mention of autocomplete</p>
                    <p>- [Added] Hashtags of autocomplete</p>
                    <p>- [Updated] Admin panel (Added page update interverl settings)</p>
                    <p>- [Fixed] Previous minor bugs</p>
                    <p>- And more minor improvements in the system</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.1.6 (<time>02 May 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Updated] API</p>
                    <p>- [Fixed] Previous minor bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.1.5 (<time>29 March 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Updated] API</p>
                    <p>- [Fixed] Previous minor bugs</p>
                    <p>- And more minor improvements in the system</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.1.4 (<time>02 March 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Added] Approval system for user ads</p>
                    <p>- [Updated] Script docs</p>
                    <p>- [Updated] API</p>
                    <p>- [Fixed] Previous minor bugs</p>
                    <p>- And more minor improvements in the system</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.1.3 (<time>24 Feb 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Added] Google Ads System for Admin</p>
                    <p>- [Updated] API</p>
                    <p>- [Added] oAuth login disable/enable system</p>
                    <p>- [Added] New welcome page</p>
                    <p>- [Added] Registration start up system</p>
                    <p>- [Added] Publication reporting system</p>
                    <p>- [Added] Live homepage feed updating system</p>
                    <p>- [Added] Post soft hidding system</p>
                    <p>- [Fixed] Follower counting errors after blocking</p>
                    <p>- [Fixed] Thumbnail error for short videos</p>
                    <p>- [Fixed] Bug with Timezone</p>
                    <p>- [Added] Ability to view avatar and profile cover in lightbox</p>
                    <p>- [Added] And also many other improvements in the admin panel and in the core of the script</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.1.2 (<time>07 Feb 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Fixed] Bugs with image orientation uploaded from the camera</p>
                    <p>- [Fixed] Bugs with uploading files and freezing the upload slider</p>
                    <p>- [Fixed] Shortcomings with dark theme on mobile and web devices</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.1.1 (<time>03 Feb 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Added] The ability to switch UI themes from admin panel (if any)</p>
                    <p>- [Added] The ability to play Vimeo videos without leaving the page</p>
                    <p>- [Added] Google Maps URL lightbox preview (embed in iFrame)</p>
                    <p>- [Improved] Links (URLs) preview system</p>
                    <p>- [Fixed] Bugs dark theme on login page</p>
                    <p>- [Improved] Some more kernel improvements to stabilize the system</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.0.9 (<time>22 Jan 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Added] The ability to play YouTube videos without leaving the page</p>
                    <p>- [Added] Ability to edit language texts, without losing changes when updating</p>
                    <p>- [Updated] Script documentation</p>
                    <p>- [Fixed] Bugs with user blocking</p>
                    <p>- [Fixed] Bugs dark theme</p>
                    <p>- [Fixed] Bugs with user removal system</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.0.8 (<time>05 Jan 2021</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Added] Controlling the length of post characters from the admin panel (600 / or arbitrary)</p>
                    <p>- [Added] System of notification settings</p>
                    <p>- [Added] API endpoints</p>
                    <p>- [Enabled] Login with username</p>
                    <p>- [Added] Dark / light theme mode</p>
                    <p>- [Added] Profile privacy (Who can see profile)</p>
                    <p>- [Improved] Post OG data system</p>
                    <p>- [Added] Post privacy (Who can reply)</p>
                    <p>- [Added] Polls system</p>
                    <p>- [Added] Video lightbox system</p>
                    <p>- [Added] Swifts system (User stories system)</p>
                    <p>- [Added] Admin ads management system</p>
                    <p>- [Added] Ad page view system (For admin)</p>
                    <p>- [Added] Follow privacy system (Who can follow me)</p>
                    <p>- [Added] GDRP system (Import my information)</p>
                    <p>- [Improved] Pages loading speed</p>
                    <p>- [Improved] Improved interface design</p>
                    <p>- [Fixed] Previous bugs</p>
                    <p>- And many more improvements in the core of the script and improved system quality</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.0.7 (<time>08 October 2020</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Fixed] Emergency updates due to previous bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.0.6 (<time>20 September 2020</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Added] SPA loading system (No hard refreshes anyomore)</p>
                    <p>- [Added] Ads system</p>
                    <p>- [Added] Affiliate system</p>
                    <p>- [Added] User wallet system</p>
                    <p>- [Added] User reporting system</p>
                    <p>- [Added] User block/unblock system</p>
                    <p>- [Added] Link OG data fetching system</p>
                    <p>- [Added] Media uploading progress bar system</p>
                    <p>- [Added] Emoji support to the chat system (Only for desktop browsers)</p>
                    <p>- [Improved] Post-media preloaders</p>
                    <p>- [Improved] Post #htags displaying system</p>
                    <p>- [Improved] User follow suggestion system</p>
                    <p>- [Fixed] Issue with domains (www)</p>
                    <p>- [Fixed] Issue with links to post on social media (Facebook, etc.)</p>
                    <p>- [Fixed] Other minor issues with the script</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.0.5 (<time>15 July 2020</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Added] User verification system</p>
                    <p>- [Added] Registration confirmation system</p>
                    <p>- [Added] User ban by admin system</p>
                    <p>- [Added] User privacy system</p>
                    <p>- [Added] Welcome page for guests</p>
                    <p>- [Added] Media files (Images, Videos) lazy loading system</p>
                    <p>- [Added] Sitemap creation system</p>
                    <p>- [Fixed] Previous several minor bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.0.4 (<time>29 June 2020</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Fixed] Previous several minor bugs (URLs in posts, image extentions support, etc..)</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.0.3 (<time>15 June 2020</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- [Feature] function for changing the username specified during registration</p>
                    <p>- [Fixed] bug with drop-down menu on profile settings page</p>
                    <p>- [Fixed] bug with pop-up message on the home page</p>
                    <p>- [Fixed] bug with function of display language for the user</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.0.2 (<time>11 June 2020</time>) [Emergency update for bug fixes]
                    </h2>
                </div>
                <div class="body">
                    <p>- [Fixed] bug with user gender difference (His/Her) In timeline</p>
                    <p>- [Fixed] bug with dropdown menu For mobile devices</p>
                    <p>- [Fixed] bug moving cover on user profile page</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.0.1 (<time>30 May 2020</time>) [Emergency update for bug fixes]
                    </h2>
                </div>
                <div class="body">
                    <p>- [Fixed] Previous several important bugs</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        v1.0.0 (<time>21 May 2020</time>)
                    </h2>
                </div>
                <div class="body">
                    <p>- Version 1.0.0 released on 21 May 2020.</p>
                </div>
            </div>
        </div>
    </div>
</div>
