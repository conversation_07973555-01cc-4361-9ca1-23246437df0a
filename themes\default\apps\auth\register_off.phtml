<form class="form"  autocomplete="off">
	<div class="form-title">
		<h2>
			<?php echo cl_translate("Oh, sorry."); ?>
		</h2>
		<p>
			<?php echo cl_translate("User registration is temporarily unavailable. <br> However, you can log in if you have previously registered. <br> If you have any questions, please contact the site administration or check back shortly"); ?>
		</p>
	</div>
	<div class="form-group">
		<label>
			<?php echo cl_translate("Username"); ?>
		</label>
        <input name="uname" disabled="true" type="text" class="form-control" placeholder="<?php echo cl_translate("Choose your username"); ?>">
    </div>
	<div class="form-group">
        <label>
        	<?php echo cl_translate("Email address"); ?>
        </label>
        <input name="email" disabled="true" type="email" class="form-control" placeholder="<?php echo cl_translate("Enter your email address"); ?>">
    </div>
	<div class="form-group">
        <label>
        	<?php echo cl_translate("Password"); ?>
        </label>
        <div class="password-ctrl">
        	<input name="password" disabled="true" class="form-control" placeholder="<?php echo cl_translate("Create a password for your account"); ?>">
        </div>
    </div>
	<div class="form-group">
        <label>
        	<?php echo cl_translate("Confirm the password"); ?>
        </label>
        <div class="password-ctrl">
        	<input name="conf_pass" disabled="true" class="form-control" placeholder="<?php echo cl_translate("Confirm the password"); ?>">
        </div>
    </div>
	<div class="form-group">
		<div class="form-tos">
			<div class="form-check">
				<input class="form-check-input" type="checkbox" value="" id="tos-agree" disabled="true">
				<label class="form-check-label" for="tos-agree">
					<span>
						<?php echo cl_translate("By continuing, you agree to {@site_name@}",array(
							"site_name" => $cl["config"]["name"]
						)); ?>
					</span>
					<a href="<?php echo cl_link('terms_of_use'); ?>"><?php echo cl_translate("Terms of Use"); ?></a> <?php echo cl_translate("And"); ?>	<a href="<?php echo cl_link('privacy_policy'); ?>"><?php echo cl_translate("Privacy policy"); ?></a>
				</label>
			</div>
		</div>
	</div>
	<div class="form-group">
		<button class="btn btn-custom main-inline lg btn-block" disabled="true">
			<?php echo cl_translate("Sign up"); ?>
		</button>
	</div>
	<div class="form-group no-mb">
		<div class="form-cta-link">
			<span>
				<?php echo cl_translate("Already have an account?"); ?>
			</span>
			<a href="<?php echo cl_link('guest'); ?>">
				<?php echo cl_translate("Login"); ?>
			</a>
		</div>
	</div>
</form>

<?php if (not_empty($cl["invite_code"]) && empty($cl["invite_code_status"])): ?>
	<script>
		jQuery(document).ready(function($) {
			setTimeout(function() {
				cl_bs_notify("<?php echo cl_translate("The invitation link you are using is invalid or has expired."); ?>", 5000, "error");
			}, 1000);
		});
	</script>
<?php endif; ?>