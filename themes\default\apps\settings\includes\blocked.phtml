<div class="timeline-container">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("settings/blocked"); ?>" data-spa="true">
						<?php echo cl_translate("Blocked users"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link("home"); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<span class="go-back" onclick="SMColibri.go_back();">
						<?php echo cl_ficon('arrow_back'); ?>
					</span>
				</div>
			</div>
		</div>
	</div>
	<div class="profile-settings">
		<div class="profile-settings__content profile-settings__content_no-padding">
			<?php if (not_empty($cl["blocked_users"])): ?>
                <div class="timeline-users-container">
                	<div class="timeline-user-ls">
	                    <?php foreach ($cl["blocked_users"] as $cl['li']): ?>
	                        <div class="user-list-item">
	                            <div class="user-list-item__avatar">
									<a href="<?php echo($cl['li']['url']); ?>" class="block-link">
										<div class="user-avatar <?php if(cl_is_online($cl['li']['is_online'])) {echo "user-avatar-online";} ?>">
											<img src="<?php echo($cl['li']['avatar']); ?>" alt="Avatar">
										</div>
									</a>
								</div>
								<div class="user-list-item__data">
									<div class="user-data">
										<div class="user-data__body">
											<div class="user-data__body-topline">
												<div class="flex-item-left">
													<div class="user-data__name">
														<a href="<?php echo($cl['li']['url']); ?>" data-spa="true" data-uinfo-lbox="<?php echo($cl['li']['id']); ?>" data-toggle="popover" data-placement="bottom">
															<span class="user-name-holder">
																<span class="user-name-holder__name">
																	<?php echo $cl['li']['name']; ?>
																</span>

																<?php if ($cl['li']['verified'] == '1'): ?>
																	<span class="user-name-holder__badge">
																		<?php echo cl_icon("verified_user_badge"); ?>
																	</span>
																<?php endif; ?>
															</span>
															<span>
																@<?php echo($cl['li']['username']); ?>
															</span>
														</a>
													</div>
													<div class="user-data__stats">
														<div class="stats-item">
															<span>
																<?php echo cl_number($cl['li']['posts']); ?>
															</span>
															<span><?php echo cl_translate("Posts"); ?></span>	
														</div>
														<div class="stats-item">
															<span>
																<?php echo cl_number($cl['li']['followers']); ?>
															</span>
															<span><?php echo cl_translate("Followers"); ?></span>	
														</div>
														<div class="stats-item">
															<span>
																<?php echo cl_number($cl['li']['following']); ?>
															</span>
															<span><?php echo cl_translate("Following"); ?></span>	
														</div>
													</div>
												</div>
												<div class="flex-item-right">
													<div class="user-data__ctrls">
														<button onclick="SMColibri.block(this);" class="btn btn-custom md main-grey" data-action="unblock" data-id="<?php echo($cl["li"]['profile_id']); ?>">
				                                            <?php echo cl_translate("Unblock"); ?>
				                                        </button>
													</div>
												</div>
											</div>

											<?php if (not_empty($cl['li']['about'])): ?>
												<div class="user-data__about">
													<?php echo $cl['li']['about']; ?>

													<?php if (not_empty($cl['li']['website'])): ?>
														| <?php echo cl_html_el("a", $cl['li']['website'], array("href" => $cl['li']['website'], "target" => "_blank", "class" => "inline-link")); ?>
													<?php endif; ?>
												</div>
											<?php endif; ?>
										</div>
									</div>
								</div>
	                        </div>
	                    <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
		</div>
	</div>
</div>