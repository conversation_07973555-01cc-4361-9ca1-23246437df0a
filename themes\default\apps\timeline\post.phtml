<?php if (not_empty($cl['li'])): ?>
	<?php if (not_empty($cl['li']['advertising'])): ?>
		<?php echo cl_template('timeline/ad'); ?>
	<?php else: ?>
		<div class="post-list-item" data-list-item="<?php echo($cl['li']['id']); ?>" data-post-offset="<?php echo($cl['li']['offset_id']); ?>" <?php echo fetch_or_get($cl['li']['attrs'],''); ?>
			 data-is-owner="<?php echo ($cl['li']['owner']['id'] == $me['id'] ? '1' : '0'); ?>"
			 data-is-following="<?php echo (!empty($cl['li']['owner']['is_following']) ? '1' : '0'); ?>"
			 data-is-verified="<?php echo ($cl['li']['owner']['verified'] == '1' ? '1' : '0'); ?>">
			<?php if (not_empty($cl['li']['me_blocked'])): ?>
				<div class="post-placeholder">
					<div class="d-flex flex-wn align-items-center">
						<div class="flex-item">
							<div class="icon">
								<?php echo cl_ficon("emoji_angry"); ?>
							</div>
						</div>
						<div class="felx-item">
							<p>
								<?php echo cl_translate("This user ({@uname@}) has blocked you from accessing their posts", array(
									"uname" => cl_html_el("a", $cl['li']['owner']['username'], array(
										"href" => $cl['li']['owner']['url'],
										"data-spa" => "true"
									))
								)); ?>
							</p>
						</div>
					</div>
				</div>
			<?php elseif (empty($cl['li']['can_see'])): ?>
				<div class="post-placeholder">
					<div class="d-flex flex-wn align-items-center">
						<div class="flex-item">
							<div class="icon">
								<?php echo cl_ficon("eye_hide"); ?>
							</div>
						</div>
						<div class="felx-item">
							<p>
								<?php echo cl_translate("Only followers of this user ({@uname@}) can see their posts", array(
									"uname" => cl_html_el("a", $cl['li']['owner']['username'], array(
										"href" => $cl['li']['owner']['url'],
										"data-spa" => "true"
									))
								)); ?>
							</p>
						</div>
					</div>
				</div>
			<?php else: ?>
				<?php if (not_empty($cl["pn"]) && $cl["pn"] == "profile"): ?>
					<?php if ($cl['li']['profile_pinned'] == "Y"): ?>
						<div class="post-list-item__alert">
							<div class="alert-badge">
								<div class="text">
									<?php echo cl_translate('{@uname@} pinned this post', array('uname' => $cl['li']['owner']['name'])); ?>
								</div>
								<div class="icon">
									<?php echo cl_ficon('pin'); ?>
								</div>
							</div>
						</div>
					<?php endif; ?>
				<?php endif; ?>

				<?php if (not_empty($cl["pn"]) &&  in_array($cl["pn"], array("explore", "feed", "home"))): ?>
					<?php if ($cl['li']['admin_pinned'] == "Y"): ?>
						<div class="post-list-item__alert post-list-item__alert_admin">
							<div class="alert-badge">
								<div class="text">
									<?php echo cl_translate('Administrator pinned this post'); ?>
								</div>
								<div class="icon">
									<?php echo cl_ficon('pin'); ?>
								</div>
							</div>
						</div>
					<?php endif; ?>
				<?php endif; ?>

				<?php if (not_empty($cl['li']['is_repost'])): ?>
					<div class="post-list-item__header">
						<div class="publication-repost">
							<?php if (not_empty($cl['li']['is_reposter'])): ?>
								<a href="<?php echo $me['url']; ?>" data-spa="true">
									<?php echo cl_translate('You reposted'); ?>
								</a>
							<?php else: ?>
								<a href="<?php echo($cl['li']['reposter']['url']); ?>" data-spa="true">
									<?php echo cl_translate('{@uname@} reposted', array('uname' => $cl['li']['reposter']['name'])) ?>
								</a>
							<?php endif; ?>

							<span class="publication-repost__icon">
								<?php echo cl_ficon('repeat'); ?>
							</span>
						</div>
					</div>
				<?php endif; ?>

				<div class="post-list-item__content">
					<div class="post-data">
						<div class="post-data__avatar">
							<a class="block-link" href="<?php echo($cl['li']['url']); ?>" data-spa="true">
								<div class="avatar-holder <?php if(cl_is_online($cl['li']['owner']['is_online'])) {echo "avatar-holder-online";} ?>">
									<img class="lozad" data-src="<?php echo($cl['li']['owner']['avatar']); ?>">
								</div> 
							</a>
						</div>
						<div class="post-data__content">
							<?php if (not_empty($cl['li']['is_blocked'])): ?>
								<div class="post-data__content-hidden" data-softhidden-post="<?php echo($cl['li']['id']); ?>">
									<div class="soft-hidden-post">
										<div class="d-flex align-items-center flex-wn">
											<div class="flex-item flex-grow-1">
												<p><?php echo cl_translate('This is a message from the user you blocked'); ?></p>
											</div>
											<div class="flex-item">
												<button class="btn btn-custom main-outline sm" onclick="SMColibri.show_post(<?php echo($cl['li']['id']); ?>, 'blocked');">
													<?php echo cl_translate('View'); ?>
												</button>
											</div>	
										</div>
									</div>
								</div>
							<?php elseif(not_empty($cl['li']['is_reported'])): ?>
								<div class="post-data__content-hidden" data-softhidden-post="<?php echo($cl['li']['id']); ?>">
									<div class="soft-hidden-post">
										<div class="d-flex align-items-center flex-wn">
											<div class="flex-item flex-grow-1">
												<p>
													<?php echo cl_translate('This post is currently under review'); ?>	
												</p>
											</div>
											<div class="flex-item">
												<button class="btn btn-custom main-outline sm" onclick="SMColibri.show_post(<?php echo($cl['li']['id']); ?>, 'reported');">
													<?php echo cl_translate('View'); ?>
												</button>
											</div>	
										</div>
									</div>
								</div>
							<?php endif; ?>

							<div class="post-data__content-inner">
								<div class="post-data-layout">
									<div class="post-data-layout__publisher">
										<div class="post-username">
											<a href="<?php echo($cl['li']['owner']['url']); ?>" data-spa="true" data-uinfo-lbox="<?php echo($cl['li']['owner']['id']); ?>" data-toggle="popover" data-placement="bottom">
												<span class="user-name-holder">
													<span class="user-name-holder__name">
														<?php echo($cl['li']['owner']['name']); ?>
													</span>

													<?php if ($cl['li']['owner']['verified'] == '1'): ?>
														<span class="user-name-holder__badge">
															<?php echo cl_icon("verified_user_badge"); ?>
														</span>
													<?php endif; ?>
												</span>
											</a>
										</div>
										<div class="post-time">
											<?php echo($cl['li']['time']); ?>

											<?php if (not_empty($cl['li']['edited'])): ?>
												<span title="<?php echo cl_translate("Edited"); ?>: <?php echo cl_date("h:i A - M d, Y", $cl['li']['edited']); ?>">
													(E)
												</span>
											<?php endif; ?>
										</div>
									</div>
									<?php if ($cl['li']['target'] == 'pub_reply' && not_empty($cl['li']['reply_to'])): ?>
										<div class="post-data-layout__target">
											<?php if (not_empty($cl['li']['reply_to']['is_owner'])): ?>
												<div class="post-reply">
													<span>
														<?php echo cl_translate('In response to your {@post_url@}', array(
															'post_url' => cl_html_el('a', cl_translate('post'), array(
																'href' => $cl['li']['reply_to']['thread_url'],
																'data-spa' => 'true'
															))
														)); ?>
													</span>
												</div>
											<?php else: ?>
												<div class="post-reply">
													<span>
														<?php
															if ($cl['li']['reply_to']['gender'] == 'M') {
																$text_temp = 'In response {@uname@} to his {@post_url@}';
															}
															
															else if ($cl['li']['reply_to']['gender'] == 'F') {
																$text_temp = 'In response {@uname@} to her {@post_url@}';
															}

															else if (in_array($cl['li']['reply_to']['gender'], array("T", "O"))) {
																$text_temp = 'In response {@uname@} to their {@post_url@}';
															}

															echo cl_translate($text_temp, array(
																'uname' => cl_html_el('a', $cl['li']['reply_to']['name'], array(
																	'href' => $cl['li']['reply_to']['url'],
																	'data-spa' => 'true'
																)),
																'post_url' => cl_html_el('a', cl_translate('publication'), array(
																	'href' => $cl['li']['reply_to']['thread_url'],
																	'data-spa' => 'true'
																))
															)); 
														?>
													</span>
												</div>
											<?php endif; ?>
										</div>
									<?php endif; ?>

									<div class="post-data-layout__content">
										<?php if ($cl['li']['is_donation_post'] == "Y"): ?>
											<?php if (not_empty($cl['li']["title"])): ?>
												<div class="publication-title">
													<?php echo($cl['li']['title']); ?>
												</div>
											<?php endif; ?>
										<?php endif; ?>

										<?php if (not_empty($cl['li']['text'])): ?>
											<?php 
												$cl['li']['text'] = cl_rn2br($cl['li']['text']);
												$cl['li']['text'] = cl_strip_brs($cl['li']['text']);
											?>
											<div class="publication-text" data-post-text="<?php echo($cl['li']['id']); ?>">
												<?php echo($cl['li']['text']); ?>
											</div>
										<?php endif; ?>

										<?php if ($cl['li']["content_view"] == true || $cl["li"]["is_free_post"] == "Y"): ?>
											<?php if ($cl['li']['type'] == 'image' && not_empty($cl['li']['media'])): ?>
												<?php if (count($cl['li']['media']) == 1): ?>
													<div class="lozad-media">
														<div class="publication-image issafe-<?php echo strtolower(fetch_or_get($cl['li']['media'][0]['is_safe'], "Y")); ?>">
															<a href="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['src'],'')); ?>" class="fbox-media">
																<img class="lozad" data-src="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['src'],'')); ?>" alt="Picture">
															</a>
														</div>
													</div>
												<?php else: ?>
													<div class="publication-images-collage">
														<?php if (count($cl['li']['media']) == 2): ?>
															<?php echo cl_template("timeline/includes/img_grid/c2_images"); ?>
														<?php elseif (count($cl['li']['media']) == 3): ?>
															<?php echo cl_template("timeline/includes/img_grid/c3_images"); ?>
														<?php elseif (count($cl['li']['media']) == 4): ?>
															<?php echo cl_template("timeline/includes/img_grid/c4_images"); ?>
														<?php elseif (count($cl['li']['media']) == 5): ?>
															<?php echo cl_template("timeline/includes/img_grid/c5_images"); ?>
														<?php elseif (count($cl['li']['media']) == 6): ?>
															<?php echo cl_template("timeline/includes/img_grid/c6_images"); ?>
														<?php elseif (count($cl['li']['media']) == 7): ?>
															<?php echo cl_template("timeline/includes/img_grid/c7_images"); ?>
														<?php elseif (count($cl['li']['media']) == 8): ?>
															<?php echo cl_template("timeline/includes/img_grid/c8_images"); ?>
														<?php elseif (count($cl['li']['media']) == 9): ?>
															<?php echo cl_template("timeline/includes/img_grid/c9_images"); ?>
														<?php elseif (count($cl['li']['media']) == 10): ?>
															<?php echo cl_template("timeline/includes/img_grid/c10_images"); ?>
														<?php endif; ?>
													</div>
												<?php endif; ?>
												<?php elseif($cl['li']['type'] == 'video' && not_empty($cl['li']['media'])): ?>
    <?php
        $video_src = cl_get_media(fetch_or_get($cl['li']['media'][0]['src'], ''));
        $video_hls = str_replace('.mp4', '.m3u8', $video_src);
        $video_id  = $cl['li']['id'];
        $poster    = cl_get_media(fetch_or_get($cl['li']['media'][0]['x']['poster_thumb'], ''));
        $ratio     = fetch_or_get($cl['li']['media'][0]['x']["ratio"], '16:9');

        // Try to get real video dimensions from the actual file
        $real_ratio = $ratio;
        $video_file_path = cl_full_path($cl['li']['media'][0]['src']);

        if (file_exists($video_file_path) && !empty($cl['li']['media'][0]['src'])) {
            try {
                if ($ratio === '8:6' || $ratio === '16:9' || empty($ratio)) {
                    require_once(cl_full_path("core/libs/getID3/getid3/getid3.php"));
                    $getID3 = new getID3;
                    $file_info = $getID3->analyze($video_file_path);

                    if (isset($file_info["video"]["resolution_x"]) && isset($file_info["video"]["resolution_y"])) {
                        $width = intval($file_info["video"]["resolution_x"]);
                        $height = intval($file_info["video"]["resolution_y"]);

                        if ($width > 0 && $height > 0) {
                            if (!function_exists('video_gcd')) {
                                function video_gcd($a, $b) {
                                    return $b ? video_gcd($b, $a % $b) : $a;
                                }
                            }
                            $gcd = video_gcd($width, $height);
                            $ratio_w = $width / $gcd;
                            $ratio_h = $height / $gcd;
                            $real_ratio = $ratio_w . ":" . $ratio_h;
                        }
                    }
                }
            } catch (Exception $e) {
                $real_ratio = $ratio;
            }
        }

        $ratio_parts = explode(':', $real_ratio);
        $width_ratio = floatval($ratio_parts[0]);
        $height_ratio = floatval($ratio_parts[1]);
        $aspect_ratio_decimal = ($height_ratio > 0) ? $width_ratio / $height_ratio : 0;
        $css_aspect_ratio = ($height_ratio > 0) ? $width_ratio . '/' . $height_ratio : '16/9';

        $is_vertical = $aspect_ratio_decimal < 1;
        $container_class = $is_vertical ? 'video-container-vertical' : 'video-container-horizontal';
    ?>
    <div class="lozad-media">
        <div class="publication-video <?php echo $container_class; ?>" data-video-ratio="<?php echo $real_ratio; ?>">
            <div class="cl-plyr-video" style="aspect-ratio: <?php echo $css_aspect_ratio; ?>; width: 100%;">
                <video
                    id="video-player-<?php echo $video_id; ?>"
                    class="plyr lazy-hls"
                    preload="none"
                    playsinline
                    controls
                    data-hls="<?php echo $video_hls; ?>"
                    data-video-id="<?php echo $video_id; ?>"
                    data-video-ratio="<?php echo $real_ratio; ?>"
                    poster="<?php echo $poster; ?>"
                    style="width: 100%; height: 100%; object-fit: contain;"
                ></video>
            </div>
        </div>
    </div>
											<?php elseif($cl['li']['type'] == 'audio' && not_empty($cl['li']['media'])): ?>
												<div class="publication-audio">
													<div class="cl-plyr-audio">
														<audio controls preload="metadata" class="plyr">
													        <source src="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['src'],'')); ?>" type="audio/mp3">
													        <source src="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['src'],'')); ?>" type="audio/mpeg">
													        <source src="<?php echo cl_get_media(fetch_or_get($cl['li']['media'][0]['src'],'')); ?>" type="audio/wav">
													    </audio>
													</div>
												</div>
											<?php elseif($cl['li']['type'] == 'document' && not_empty($cl['li']['media'])): ?>
												<div class="publication-document">
													<a href="<?php echo cl_get_media($cl['li']['media'][0]['src']); ?>" target="_blank" class="document-file">
														<div class="document-file__icon">
															<?php echo cl_ficon("document"); ?>
														</div>
														<div class="document-file__body">
															<?php echo($cl['li']['media'][0]['x']["filename"]); ?>
														</div>
														<div class="document-file__type">
															<?php echo($cl['li']['media'][0]['x']["file_type"]); ?>-<?php echo cl_translate("FILE"); ?>
														</div>
														<div class="document-file__icon">
															<?php echo cl_ficon("open"); ?>
														</div>
													</a>
												</div>
											<?php elseif($cl['li']['type'] == 'gif' && not_empty($cl['li']['media'])): ?>
												<div class="lozad-media">
													<div class="publication-image">
														<a href="<?php echo fetch_or_get($cl['li']['media'][0]['src'],''); ?>" class="fbox-media">
															<img class="lozad" data-src="<?php echo fetch_or_get($cl['li']['media'][0]['src'],''); ?>" alt="GIF-Image">
														</a>
													</div>
												</div>
											<?php elseif($cl['li']['type'] == 'poll' && not_empty($cl['li']['poll'])): ?>
												<div class="publication-poll" data-post-poll="<?php echo($cl['li']['id']); ?>" data-status="<?php echo($cl['li']['poll']['has_voted']); ?>" data-stopped="<?php echo($cl['li']['poll_status']); ?>">
													<div class="publication-poll__inner">
														<?php foreach ($cl['li']['poll']['options'] as $i => $poll_data): ?>
															<div class="publication-poll__option" onclick="SMColibri.vote_poll(<?php echo($cl['li']['id']); ?>, <?php echo($i); ?>);" data-poll-option="<?php echo($i); ?>">
																<div class="bar-icon">
																	<?php echo cl_ficon("checkmark_circle"); ?>
																</div>
																<div class="bar-text">
																	<p>
																		<?php echo $poll_data["option"]; ?>
																	</p>
																</div>
																<div class="bar-num">
																	<b>
																		<?php if (not_empty($cl['li']['poll']['has_voted'])): ?>
																			<?php echo $poll_data["percentage"]; ?>%
																		<?php endif; ?>
																	</b>
																</div>

																<?php if (not_empty($cl['li']['poll']['has_voted'])): ?>
																	<span class="bar-slider" style="width: <?php echo $poll_data["percentage"]; ?>%;"></span>
																<?php else: ?>
																	<span class="bar-slider"></span>
																<?php endif; ?>
															</div>
														<?php endforeach; ?>

														<div class="publication-poll__total-votes">
															<span data-an="total-poll-voted">
																<?php
																	$total_votes = 0;
																	$poll_data = json($cl['li']['poll_data']);

																	foreach ($poll_data as $pod) {
																		$total_votes = ($total_votes += $pod["votes"]);
																	}

																	echo  $total_votes;
																?>
															</span>

															<?php echo cl_translate("people voted"); ?>

															<?php if ($cl['li']["poll_status"] == "stopped"): ?>
																- <?php echo cl_translate("Poll timed out"); ?>
															<?php endif; ?>
														</div>
													</div>
												</div>
											<?php elseif(not_empty($cl['li']['og_data'])): ?>
												<?php if (not_empty($cl['li']['og_data']['video_embed'])): ?>
													<div class="publication-og">
														<div class="publication-og__inner embeded-iframe">
															<div class="publication-og__image">
																<div class="lozad-media">
																	<a href="<?php echo($cl['li']['og_data']['video_embed']); ?>" class="fbox-media">
																		<img class="lozad" data-src="<?php echo($cl['li']['og_data']['image']); ?>" alt="Video">
																		<div class="video-play-button">
																			<span class="video-play-button__arrow">
																				<?php echo cl_ficon("play"); ?>
																			</span>
																		</div>
																	</a>
																</div>
															</div>
															<div class="publication-og__description">
																<h5>
																	<?php echo($cl['li']['og_data']['title']); ?>
																</h5>
																<?php if (not_empty($cl['li']['og_data']['description'])): ?>
																	<p>
																		<?php echo($cl['li']['og_data']['description']); ?>
																	</p>
																<?php else: ?>
																	<p>
																		<a target="_blank" href="<?php echo($cl['li']['og_data']['video_embed']); ?>">
																			<?php echo($cl['li']['og_data']['video_embed']); ?>
																		</a>
																	</p>
																<?php endif; ?>
															</div>
														</div>
													</div>
												<?php else: ?>
													<div class="publication-og">
														<div class="publication-og__inner link" data-href="<?php echo($cl['li']['og_data']['url']); ?>">
															<?php if (not_empty($cl['li']['og_data']['image'])): ?>
																<div class="publication-og__image">
																	<img src="<?php echo($cl['li']['og_data']['image']); ?>" alt="IMG">
																</div>
															<?php endif; ?>
															
															<div class="publication-og__description">
																<h5>
																	<?php echo($cl['li']['og_data']['title']); ?>
																</h5>
																<p>
																	<?php echo($cl['li']['og_data']['description']); ?>
																</p>
																<a href="<?php echo($cl['li']['og_data']['url']); ?>" target="_blank">
																	<?php echo($cl['li']['og_data']['url']); ?>
																</a>
															</div>
														</div>
													</div>
												<?php endif; ?>
											<?php endif; ?>
										<?php else: ?>
											<div class="publication-paid">
												<div class="publication-paid__header">
													<div class="pubowner-avatar">
														<img class="lozad" data-src="<?php echo($cl['li']['owner']['avatar']); ?>">
													</div>
													<div class="pubowner-avatar">
														<img class="lozad" data-src="<?php echo cl_link("themes/default/statics/img/premium-avatar.png"); ?>">
													</div>
												</div>
												<div class="publication-paid__body">
													<h5>
														<?php echo cl_translate("Subscribe to Unlock"); ?>
													</h5>
													<p>
														<?php echo cl_translate("For {@price@} / Monthly", array("price" => cl_money($cl["li"]['owner']['subscription_price']))); ?>
													</p>
												</div>
												<div class="publication-paid__footer">
													<button class="btn btn-custom main-inline lg btn-block" onclick="SMColibri.subscribe(<?php echo $cl['li']['user_id']; ?>);">
														<?php echo cl_translate("Subscribe now"); ?> / <?php echo cl_money($cl["li"]['owner']['subscription_price']); ?>
													</button>
												</div>
											</div>
										<?php endif; ?>

										<?php if ($cl['li']["content_view"] == true): ?>
											<?php if ($cl['li']['is_donation_post'] == "Y"): ?>
												<div class="publication-funding">
													<div class="publication-funding__header">
														<?php echo cl_translate("{@raised@} raised of {@goal@} goal", array(
															"raised" => cl_html_el("span", cl_money($cl["li"]["donation_raised"]), array("class" => "raised", "data-an" => "donation-raised")),
															"goal" => cl_money($cl["li"]["donation_amount"])
														)); ?>
													</div>
													<div class="publication-funding__body">
														<div class="funding-progress">
															<div class="funding-progress__bar" data-an="donation-raised-percent" style="width: <?php echo $cl["li"]["donation_raised_percent"] . "%;"; ?>;"></div>
														</div>
														<div class="funding-total">
															<span>
																<span data-an="donations-total"><?php echo($cl["li"]["donations_total"]); ?></span> <?php echo cl_translate("donations"); ?>
															</span>
															<span>
																<span data-an="donations-left-amount">
																	<?php
																		if ($cl["li"]["donation_raised"] >= $cl["li"]["donation_amount"]) {
																			echo cl_money("0.00");
																		}
																		else{
																			echo cl_money($cl["li"]["donation_amount"] - $cl["li"]["donation_raised"]);
																		}
																	 ?>
																</span>

																<?php echo cl_translate("to go"); ?>
															</span>
														</div>
													</div>
													<?php if ($cl["li"]["is_owner"] != true): ?>
														<div class="publication-funding__footer">
															<button onclick="SMColibri.donate_post(<?php echo($cl["li"]["id"]); ?>)" class="btn target-url-btn btn-custom main-green lg btn-block">
																<?php echo cl_translate("Donate now"); ?>
															</button>
														</div>
													<?php else: ?>
														<div class="publication-funding__footer">
															<button onclick="SMColibri.donate_self();" class="btn target-url-btn btn-custom main-green lg btn-block">
																<?php echo cl_translate("Donate now"); ?>
															</button>
														</div>
													<?php endif; ?>
												</div>
											<?php endif; ?>
										<?php endif; ?>
									</div>

									<div class="post-data-layout__controls">
										<?php if (empty($cl['li']['has_liked'])): ?>
											<button class="ctrls-item" onclick="SMColibri.like_post('<?php echo $cl['li']['id']; ?>', this);">
												<span class="ctrls-item__icon">
													<?php echo cl_ficon('thumb_like'); ?>
												</span>
												<span class="num" data-an="likes-count">
													<?php echo $cl['li']['likes_count']; ?>
												</span>
											</button>
										<?php else: ?>
											<button class="ctrls-item liked" onclick="SMColibri.like_post('<?php echo $cl['li']['id']; ?>', this);">
												<span class="ctrls-item__icon">
													<?php echo cl_ficon('thumb_like'); ?>
												</span>
												<span class="num" data-an="likes-count">
													<?php echo $cl['li']['likes_count']; ?>
												</span>
											</button>
										<?php endif; ?>

										<button class="ctrls-item">
											<a class="ctrls-item__inner-link" href="<?php echo $cl['li']['url']; ?>" data-spa="true">
												<span class="ctrls-item__icon">
													<?php echo cl_ficon('comment'); ?>
												</span>
												<span class="num"><?php echo $cl['li']['replys_count']; ?></span>
											</a>
										</button>
										
										<?php if (empty($cl['li']['has_reposted'])): ?>
											<button onclick="SMColibri.repost('<?php echo $cl['li']['id']; ?>', this);" class="ctrls-item" data-an="repost-ctrl">
												<span class="ctrls-item__icon">
													<?php echo cl_ficon('repeat'); ?>
												</span>
												<span class="num" data-an="reposts-count">
													<?php echo $cl['li']['reposts_count']; ?>
												</span>
											</button>
										<?php else: ?>
											<button onclick="SMColibri.repost('<?php echo $cl['li']['id']; ?>', this);" class="ctrls-item reposted" data-an="repost-ctrl">
												<span class="ctrls-item__icon">
													<?php echo cl_ficon('repeat'); ?>
												</span>
												<span class="num" data-an="reposts-count">
													<?php echo $cl['li']['reposts_count']; ?>
												</span>
											</button>
										<?php endif; ?>

										<button class="ctrls-item" onclick="SMColibri.share_post('<?php echo $cl['li']['url']; ?>','<?php echo urlencode($cl['li']['url']); ?>');">
											<span class="ctrls-item__icon">
												<?php echo cl_ficon('share'); ?>
											</span>
										</button>

										<?php echo cl_template("timeline/post_components/dropdown_menu"); ?>
									</div>
								</div>
							</div>	
						</div>
					</div>
				</div>
			<?php endif;?>
		</div>

		<?php if (cl_is_feed_gad_allowed()): ?>
			<?php if (not_empty($cl["gads_horiz"])): ?>
				<?php if (cl_show_feed_gad()): ?>
					<div class="cl-google-ads centered">
						<?php echo $cl["gads_horiz"]; ?>
					</div>
				<?php endif; ?>
			<?php endif; ?>
		<?php endif; ?>
	<?php endif; ?>
<?php endif; ?>