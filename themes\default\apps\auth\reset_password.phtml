<form class="form" id="cl-resetpass-vue-app" v-on:submit="submit_form($event)" autocomplete="off">
    <div class="form-group-set" v-if="process_succeeded != true">
        <div class="form-title">
            <h2>
                <?php echo cl_translate("Password recovery"); ?>   
            </h2>
            <p>
                <?php echo cl_translate("Forgot your password? Please enter your email address. You will receive a link to create a new password by email"); ?>
            </p>
        </div>
    	<div class="form-group">
            <label><?php echo cl_translate("Email address"); ?></label>
            <input name="email" v-model.trim="$v.email.$model" type="email" class="form-control" placeholder="<?php echo cl_translate("Enter your email address mail"); ?>">
            <div class="invalid-main-feedback" v-if="is_valid_email">
                {{invalid_feedback_email}}
            </div>
        </div>
        <div class="form-group" v-if="process_failed">
            <div class="invalid-main-feedback">
                <?php echo cl_translate("Failed to complete the password recovery process, please check your details and try again later!"); ?>
            </div>
        </div>
    	<div class="form-group">
			<button v-if="submitting != true" v-bind:disabled="is_valid_form != true" class="btn btn-custom main-inline lg btn-block">
				<?php echo cl_translate("Reset the password"); ?>
			</button>
            <button v-else disabled="true" type="button" class="btn btn-custom main-inline lg btn-block">
                <?php echo cl_translate("Please wait"); ?>
            </button>
		</div>
        <div class="form-group no-mb">
            <div class="form-cta-link">
                <span>
                    <?php echo cl_translate("Remembered your password?"); ?>
                </span>
                <a href="<?php echo cl_link('guest'); ?>">
                    <?php echo cl_translate("Login"); ?>
                </a>
            </div>
        </div>
        <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
    </div>
    <div class="form-group-set" v-else>
        <div class="form-title">
            <h2>
                <?php echo cl_translate("Email sent"); ?>         
            </h2>
            <p>
                <?php echo cl_translate("Please check your email inbox. mail, we sent you an email"); ?>
            </p>
        </div>
        <div class="form-group">
            <div class="valid-main-feedback text-center">
                <?php echo cl_translate("We have sent you an email to reset your password. Please follow the instructions in the email to reset your password."); ?>
            </div>
        </div>
        <div class="form-group">
            <a href="<?php echo cl_link('guest'); ?>">
                <button type="button" class="btn btn-custom main-inline lg btn-block">
                    <?php echo cl_translate("Okey"); ?>
                </button>
            </a>
        </div>
        <div class="form-group no-mb">
            <div class="form-cta-link">
                <span>
                    <?php echo cl_translate("Remembered your password?"); ?>
                </span>
                <a href="<?php echo cl_link('guest'); ?>">
                    <?php echo cl_translate("Login"); ?>
                </a>
            </div>
        </div>
    </div>
</form>
