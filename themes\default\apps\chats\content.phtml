<div class="timeline-container" data-app="chat">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link('chats'); ?>" data-spa="true">
						<?php echo cl_translate('Messages'); ?>
					</a> 
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link('/'); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<span class="go-back" onclick="SMColibri.go_back();">
						<?php echo cl_ficon('arrow_back'); ?>
					</span>
				</div>
			</div>
		</div>
	</div>

	<?php if (not_empty($cl["chats"])): ?>
		<div class="d-block" id="vue-chats-app">
			<div class="timeline-searchbar">
				<form class="form timeline-searchbar__form" autocomplete="off">
					<div class="keyword-input">
						<input v-model.trim="search_query" v-on:input="search_chats" type="text" placeholder="<?php echo cl_translate("Search contacts"); ?>">
						
						<span class="keyword-input__right-icon" v-if="search_query" v-on:click="cancel_search">
							<?php echo cl_ficon('dismiss'); ?>
						</span>
						<span class="keyword-input__left-icon" v-else>
							<?php echo cl_ficon('search'); ?>
						</span>
					</div>
				</form>
			</div>
			<div class="chats">
				<div v-show="display_chats" class="chat-contacts" data-an="contacts-list">
					<?php foreach ($cl["chats"] as $cl['li']): ?>
						<?php echo cl_template('chats/includes/list_item'); ?>
					<?php endforeach; ?>
				</div>
				<div v-show="(display_chats != true)" class="timeline-placeholder">
					<div class="icon">
						<div class="icon__bg">
							<?php echo cl_ficon('people_search'); ?>
						</div>
					</div>
					<div class="pl-message">
						<h4>
							<?php echo cl_translate("Nothing found!"); ?>
						</h4>
						<p>
							<?php echo cl_translate("Could not find anything in your chats history for your search query {@search_query@}. Please try again by typing other keywords.", array(
								"search_query" => cl_html_el('b', '{{search_query}}', array('class' => 'seach-query'))
							)); ?>
						</p>
					</div>
				</div>
			</div>
		</div>
		<?php echo cl_template('chats/scripts/app_master_script'); ?>
	<?php else: ?>
		<div class="timeline-placeholder">
			<div class="icon">
				<div class="icon__bg">
					<?php echo cl_ficon('chat'); ?>
				</div>
			</div>
			<div class="pl-message">
				<h4>
					<?php echo cl_translate("No chats yet!"); ?>
				</h4>
				<p>
					<?php echo cl_translate("Oops! It looks like you don't have any chat history yet. To start chatting with a user, open his profile page, then click on the button with {@svg_icon@} to start chatting",array(
						"svg_icon" => cl_html_el("span", cl_ficon("chat"), array("class" => "inline-icon"))
					)); ?>
				</p>
			</div>
		</div>
	<?php endif; ?>
</div>

