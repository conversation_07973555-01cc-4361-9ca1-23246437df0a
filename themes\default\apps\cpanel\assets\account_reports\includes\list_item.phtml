<tr data-list-item="<?php echo($cl['li']['id']); ?>" data-user-id="<?php echo($cl['li']['profile_id']); ?>">
    <td>
        <?php echo($cl['li']['id']); ?>
    </td>
    <td>
        <div class="user-info-holder">
            <div class="avatar-holder">
                <img src="<?php echo($cl['li']['u1_avatar']); ?>" alt="Avatar">
            </div>
            <div class="uname-holder">
                <b>
                    <span class="user-name-holder <?php if ($cl['li']['u1_verified'] == '1') {echo('verified-badge');} ?>">
                        <?php echo($cl['li']['u1_name']); ?>
                    </span>
                </b>
                <a href="<?php echo($cl['li']['u1_url']); ?>">
                    <?php echo($cl['li']['u1_username']); ?>
                </a>
            </div>
        </div>
    </td>
    <td>
        <span>
            <?php echo $cl['profile_report_types'][$cl['li']['reason']]; ?>
        </span>
    </td>
    <td>
        <div class="user-info-holder">
            <div class="avatar-holder">
                <img src="<?php echo($cl['li']['u2_avatar']); ?>" alt="Avatar">
            </div>
            <div class="uname-holder">
                <b>
                    <span class="user-name-holder <?php if ($cl['li']['u2_verified'] == '1') {echo('verified-badge');} ?>">
                        <?php echo($cl['li']['u2_name']); ?>
                    </span>
                </b>
                <a href="<?php echo($cl['li']['u2_url']); ?>">
                    <?php echo($cl['li']['u2_username']); ?>
                </a>
            </div>
        </div>
    </td>
    <td>
        <time>
            <?php echo($cl['li']['time']); ?>
        </time>
    </td>
    <td>
        <?php if ($cl['li']['seen'] == '0'): ?>
            <span data-an="report-status" class="badge bg-red">
                Not viewed
            </span>
        <?php else: ?>
            <span data-an="report-status" class="badge bg-orange">
                Viewed
            </span>
        <?php endif; ?>
    </td>
    <td>
        <div class="dropdown">
            <a href="javascript:void(0);" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                <?php echo cl_ficon("chevron_down"); ?>
            </a>
            <div class="dropdown-menu dropdown-menu-right">
                <a href="javascript:void(0);" class="dropdown-item" onclick="CSMAccountReports.show('<?php echo($cl['li']['id']); ?>');">
                    Show details
                </a>
                <a href="<?php echo($cl['li']['u2_url']); ?>" class="dropdown-item">
                    Profile
                </a>
                <a href="javascript:void(0);" class="dropdown-item" onclick="SMC_CPanel.delete_user('<?php echo($cl['li']['profile_id']); ?>');">
                    Delete
                </a>
                <a href="javascript:void(0);" class="dropdown-item" onclick="CSMAccountReports.ignore('<?php echo($cl['li']['id']); ?>');">
                    Ignore
                </a>
            </ul>
        </div>
    </td>
</tr>