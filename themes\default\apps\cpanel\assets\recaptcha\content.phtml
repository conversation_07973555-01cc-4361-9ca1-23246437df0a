<div class="cp-app-container" data-app="recaptcha">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                Google reCAPTCHA
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        Manage Google reCAPTCHA system
                    </h2>
                </div>
                <div class="body">
                    <div class="inline-alertbox-wrapper">
                        <div class="inline-alertbox info">
                            <div class="icon">
                                <?php echo cl_ficon("info"); ?>
                            </div>
                            <div class="alert-message">
                                <p>
                                    To use this function on your website You need to register your domain and get an API key from google to use reCAPTCHA. Without registering your domain, the captcha widget won’t load on your page.
                                    <br>
                                    <br>
                                    <b>
                                        Register your website at - <a href="https://www.google.com/recaptcha/admin" target="_blank">https://www.google.com/recaptcha/admin</a>
                                    </b>
                                </p>
                            </div>
                        </div>
                    </div>
                    <form class="form" data-an="form">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group" data-an="google_recaptcha-input">
                                    <label>
                                        reCAPTCHA
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="google_recaptcha" class="form-control">
                                            <option value="on" <?php if($cl['config']['google_recaptcha'] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl['config']['google_recaptcha'] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        Enable google reCAPTCHA on your website to prevent spam.
                                    </small>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group" data-an="google_recap_key1-input">
                                    <label>
                                        Sitekey
                                    </label>
                                    <div class="form-line">
                                        <div class="form-line">
                                            <input value="{%config google_recap_key1%}" name="google_recap_key1" type="text" class="form-control" placeholder="Google reCAPTCHA Site key">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group" data-an="google_recap_key2-input">
                                    <label>
                                        Secret key
                                    </label>
                                    <div class="form-line">
                                        <div class="form-line">
                                            <input value="{%config google_recap_key2%}" name="google_recap_key2" type="text" class="form-control" placeholder="Google reCAPTCHA Secret key">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group no-mb">
                       		<button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                Save changes
                            </button>
                        </div>
                        <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo cl_template('cpanel/assets/recaptcha/scripts/app_master_script'); ?>