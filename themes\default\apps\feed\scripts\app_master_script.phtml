<script>
	"use strict";

	var offset = 30;

	jQuery(document).ready(function($) {	
		var _app    = $('div[data-app="feed"]');
		var feed_ls = _app.find('[data-an="entry-list"]');

		_app.find('button[data-an="load-more"]').on('click', function(event) {
			event.preventDefault();
			
			var _self = $(this);
			

			$.ajax({
				url: "<?php echo cl_link("native_api/feed/load_more"); ?>",
				type: 'GET',
				dataType: 'json',
				data: {
					offset: offset,
				},
				beforeSend: function(){
					_self.attr('disabled', 'true').text("<?php echo cl_translate("Please wait"); ?>");
				}
			}).done(function(data) {
				if (data.status == 200) {
					feed_ls.append(data.html);

					_self.removeAttr('disabled').text("<?php echo cl_translate("Show more"); ?>");

					offset += 30;
				}

				else {
					_self.text("<?php echo cl_translate("That is all for now!"); ?>");
				}
			});
		});
	});
</script>