<div class="left-sb-container sidebar" data-app="left-sidebar">
	<div class="sidebar__inner">
		<div class="sidebar__nav">
			<div class="sidebar__nav-group">
<div style=" padding-left: 2px;" >
				<div data-navitem="profile" class="sidebar__nav-item <?php if($cl['pn'] == 'profile' && not_empty($cl["page_xdata"]) && not_empty($cl["page_xdata"]["is_me"])) {echo('active');} ?>">
					<span class="icon avatar animate__animated animate__jackInTheBox" style="width: 30px; height: 30px;">
						<img src="<?php echo($me['avatar']); ?>" alt="Avatar" width="55" height="55">
					</span>
					<span class="text" style="margin-left: -1px !important;">
						<a href="<?php echo($me["url"]); ?>" data-spa="true">
							<?php echo($me["name"]); ?>
							<?php if ($cl['me']['verified'] == '1'): ?>
								<span>
									<img style="margin-top: 0px !important;" src="/themes/default/statics/md_icons/verified_user_badge.svg" width="14" height="14">
								</span>
							<?php endif; ?>
						</a>
					</span>
					<?php if ($cl['me']['is_premium'] == '1'): ?>
<span style="margin-left: 3px; margin-top: -3px;">
		<img src="{%config theme_url%}/statics/img/vip.png" width="16" height="16">
</span>
<?php endif; ?>
</div>
				</div>
				
				<div data-navitem="home" class="sidebar__nav-item <?php if($cl['pn'] == 'home') {echo('active');} ?>">
					<span class="icon">
						<?php echo cl_ficon('home'); ?>
					</span>
					<span class="text">
						<a href="<?php echo cl_link('/'); ?>" >
							<?php echo cl_translate('Home'); ?>
						</a>
					</span>
				</div>
				<div data-navitem="search" class="sidebar__nav-item <?php if($cl['pn'] == 'search') {echo('active');} ?>">
					<span class="icon">
						<?php echo cl_ficon('compass_northwest'); ?>
					</span>
					<span class="text">
						<a href="<?php echo cl_link("search"); ?>" data-spa="true">
							<?php echo cl_translate('Explore'); ?>
						</a>
					</span>
				</div>
				<div data-navitem="notifications" class="sidebar__nav-item <?php if($cl['pn'] == 'notifications') {echo('active');} ?>">
					<span class="icon">
						<?php echo cl_ficon('notifications'); ?>
					</span>
					<span class="text">
						<a href="<?php echo cl_link("notifications"); ?>" data-spa="true">
							<?php echo cl_translate('Notifications'); ?>
							<span id="playsound(new-notifs)" class="info-indicators" data-an="new-notifs" ><?php echo fetch_or_get($me['new_notifs']); ?></span>
						</a>
					</span>
				</div>
				<div data-navitem="chat" class="sidebar__nav-item <?php if($cl['pn'] == 'chat') {echo('active');} ?>">
					<span class="icon">
						<?php echo cl_ficon('chat'); ?>
					</span>
					<span class="text">
						<a href="<?php echo cl_link("chats"); ?>" data-spa="true">
							<?php echo cl_translate('Messages'); ?>
							<span class="info-indicators" data-an="new-messages"><?php echo fetch_or_get($me['new_messages']); ?></span>
						</a>
					</span>
				</div>
				<?php if ($me["is_premium"] == 1): ?>
				<div data-navitem="subscriptions" class="sidebar__nav-item <?php if($cl['pn'] == 'subscriptions') {echo('active');} ?>">
					<span class="icon">
						<?php echo cl_ficon('person_tag'); ?>
					</span>
					<span class="text">
						<a href="<?php echo cl_link("subscriptions"); ?>" data-spa="true">
							<?php echo cl_translate('Subscriptions'); ?>
						</a>
					</span>
				</div>
				<?php endif; ?>
			
				<?php if ($cl['config']['prem_account_system_status'] == 'on'): ?>
					<?php if ($me["is_premium"] == 1): ?>
						<div data-navitem="premium_features" class="sidebar__nav-item <?php if($cl['pn'] == 'premium_features') {echo('active');} ?>">
							<span class="icon">
								<?php echo cl_ficon('premium'); ?>
							</span>
							<span class="text">
								<a href="<?php echo cl_link("premium_features"); ?>" data-spa="true">
									<?php echo cl_translate('Premium features'); ?>
								</a>
							</span>
						</div>
					<?php else: ?>
						<div data-navitem="premium_account" class="sidebar__nav-item <?php if($cl['pn'] == 'premium_account') {echo('active');} ?>">
							<span class="icon" >
							<img src="{%config theme_url%}/statics/img/vip.png" width="24" height="24">
							</span>
							<span class="text" style=" font-weight:bolder !important; margin-top: 3px; ">
								<a href="<?php echo cl_link("premium_account"); ?>" data-spa="true">
									<?php echo cl_translate('Premium account'); ?>
								</a>
							</span>
						</div>
					<?php endif; ?>
				<?php endif; ?>
				
				<div data-navitem="bookmarks" class="sidebar__nav-item <?php if($cl['pn'] == 'bookmarks') {echo('active');} ?>">
					<span class="icon">
						<?php echo cl_ficon('bookmark'); ?>
					</span>
					<span class="text">
						<a href="<?php echo cl_link("bookmarks"); ?>" data-spa="true">
							<?php echo cl_translate('Bookmarks'); ?>
						</a>
					</span>
				</div>
			</div>
			<div class="sidebar__nav-group sidebar__nav-group_collapse">
				
				<div class="sidebar__nav-item-devider"></div>
				<?php if (not_empty($cl['is_admin'])): ?>
					<div class="sidebar__nav-item">
						<span class="icon">
							<?php echo cl_ficon('shield'); ?>
						</span>
						<span class="text">
							<a href="<?php echo cl_link("admin_panel"); ?>" target="_blank">
								<?php echo cl_translate('Control panel'); ?>
							</a>
						</span>
					</div>
				<?php endif; ?>
				<div class="sidebar__nav-item-devider"></div>
				<div data-navitem="wallet" class="sidebar__nav-item <?php if($cl['pn'] == 'wallet') {echo('active');} ?>">
					<span class="icon">
						<?php echo cl_ficon('wallet'); ?>
					</span>
					<span class="text">
						<a href="<?php echo cl_link("wallet"); ?>" data-spa="true">
							<?php echo cl_translate('Wallet'); ?> <b class="wallet">(<?php echo cl_money($me['wallet']); ?>)</b>
						</a>
					</span>
				</div>
				<?php if ($cl['config']['advertising_system'] == 'on'): ?>
					<div data-navitem="ads" class="sidebar__nav-item <?php if($cl['pn'] == 'ads') {echo('active');} ?>">
						<span class="icon">
							<?php echo cl_ficon('ads'); ?>
						</span>
						<span class="text">
							<a href="<?php echo cl_link("ads"); ?>" data-spa="true">
								<?php echo cl_translate('Advertising'); ?>
							</a>
						</span>
					</div>
				<?php endif; ?>
				<?php if ($cl['config']['affiliates_system'] == 'on'): ?>
					<div data-navitem="affiliates" class="sidebar__nav-item <?php if($cl['pn'] == 'affiliates') {echo('active');} ?>">
						<span class="icon">
							<?php echo cl_ficon('group_work'); ?>
						</span>
						<span class="text">
							<a href="<?php echo cl_link("affiliates"); ?>" data-spa="true">
								<?php echo cl_translate('Affiliates'); ?>
							</a>
						</span>
					</div>
				<?php endif; ?>
				
				<div class="sidebar__nav-item-devider"></div>
						
				<div class="sidebar__nav-item">
					<span class="icon">
						<?php echo cl_ficon('content_settings'); ?>
					</span>
					<span class="text">
						<a href="javascript:void(0);" onclick="SMColibri.display_settings();">
							<?php echo cl_translate('Display settings'); ?>
						</a>
					</span>
				</div>
				<div class="sidebar__nav-item">
					<span class="icon">
						<?php echo cl_ficon('sign_out'); ?>
					</span>
					<span class="text">
						<a href="javascript:void(0);" onclick="SMColibri.logout();">
							<?php echo cl_translate('Logout'); ?>
						</a>
					</span>
				</div>
				<div class="sidebar__nav-item-devider"></div>
			
			</div>
		
			<div class="sidebar__nav-group sidebar__nav-group_toggle">
				<div class="sidebar__nav-item-devider"></div>
				<div class="sidebar__nav-item" onclick="SMColibri.toggleSBMenu(this);">
					<span class="icon">
						<?php echo cl_ficon('more_horiz'); ?>
					</span>
					<span class="text">
						<a href="javascript:void(0);" >
							<?php echo cl_translate('More'); ?>
						</a>
					</span>
					<span>
						
					</span>
				</div>
				
				<?php if (in_array($cl['me']['active'], array("0", "2"))): ?>
<div class="publish-post-btn h-mobile" style="margin-top: 40px;">
		
		</div>
		<?php else: ?>
			<div class="publish-post-btn h-mobile" style="margin-top: 40px;">
			<button class="btn btn-custom main-inline lg home btn-block" data-toggle="modal"
				data-target="#add_new_post">
				<?php echo cl_translate('New post'); ?>
			</button>
		</div>
		<?php endif; ?>
			</div>

		</div>
		<div class="sidebar__footer">
			<?php echo cl_template('main/footer'); ?>
		</div>
	</div>
</div>

