﻿button.btn{
    font-size: 15px;
    border: none;
    box-shadow: none;
    padding: 10px 32px;
    font-weight: 500;
    border-radius: 5em;
    outline: 0;

    &-primary{
        background-color: $black;

        &:hover, &:active, &:focus{
            background-color: lighten($black, 10);
            box-shadow: none;
        }
    }

    &-secondary{
        background-color: darken($gray-bg-1, 3);
        color: $grey;

        &:hover, &:active, &:focus{
            background-color: darken($gray-bg-1, 5);
            box-shadow: none;
            color: $grey;
        }

        &:disabled{
            background-color: darken($gray-bg-1, 3);
            color: $grey;
            outline: 0;
            box-shadow: none;
        }
    }
}
