<form class="form" id="cl-savepass-vue-app" v-on:submit="submit_form($event)" autocomplete="off">
	<div class="form-title">
		<h2><?php echo cl_translate("New password"); ?></h2>
		<p>
           <?php echo cl_translate("To update your password, enter a new one below. Your password must be at least 6 characters. Use uppercase and lowercase letters, numbers and special characters, for example (? $% # @)"); ?> 
		</p>
	</div>
	<div class="form-group">
        <label><?php echo cl_translate("Password"); ?></label>
        <div class="password-ctrl">
            <input v-model.trim.lazy="$v.password.$model" v-bind:type="password1_display" name="password" class="form-control" placeholder="<?php echo cl_translate("Enter new password"); ?>">
            
            <button class="password-ctrl" type="button" v-on:click="password1_display_toggle">
                <span v-if="password1_display == 'password'">
                    <?php echo cl_ficon("eye_show"); ?>
                </span>
                <span v-else>
                    <?php echo cl_ficon("eye_hide"); ?>
                </span>
            </button>
        </div>
        <div class="invalid-main-feedback" v-if="is_valid_password">
            {{invalid_feedback_pass}}
        </div>
    </div>
    <div class="form-group">
        <label><?php echo cl_translate("Repeat password"); ?></label>
        <div class="password-ctrl">
            <input v-model.trim="$v.password2.$model" v-bind:type="password2_display" name="conf_pass" class="form-control" placeholder="<?php echo cl_translate("Confirm new password"); ?>">
        
            <button class="password-ctrl" type="button" v-on:click="password2_display_toggle">
                <span v-if="password2_display == 'password'">
                    <?php echo cl_ficon("eye_show"); ?>
                </span>
                <span v-else>
                    <?php echo cl_ficon("eye_hide"); ?>
                </span>
            </button>
        </div>
        <div class="invalid-main-feedback" v-if="is_valid_password2">
            {{invalid_feedback_pass2}}
        </div>
    </div>
    <div class="form-group" v-if="process_failed">
        <div class="invalid-main-feedback">
            <?php echo cl_translate("An error occurred while trying to save the new password, please check that the data you entered is correct and try again!"); ?>
        </div>
    </div>
	<div class="form-group no-mb">
        <button v-if="submitting != true" v-bind:disabled="is_valid_form != true" class="btn btn-custom main-inline lg btn-block">
            <?php echo cl_translate("Save new password"); ?>
        </button>
        <button v-else disabled="true" type="button" class="btn btn-custom main-inline lg btn-block">
            <?php echo cl_translate("Please wait"); ?>
        </button>
	</div>
    <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
    <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['em_code'],'none'); ?>" name="em_code">
</form>
