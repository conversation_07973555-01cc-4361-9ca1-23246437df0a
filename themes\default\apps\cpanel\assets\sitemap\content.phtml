<div class="cp-app-container" data-app="sitemap">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                Sitemap
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        Generate sitemap
                    </h2>
                </div>
                <div class="body">
                    <form class="form" data-an="form">
                        <div class="inline-alertbox-wrapper">
                            <div class="inline-alertbox warning">
                                <div class="icon">
                                    <?php echo cl_ficon("warning"); ?>
                                </div>
                                <div class="alert-message">
                                    <p>
                                        Please note that the process of creating a site map can take several minutes or even more, depending on the amount of content on your site, so after clicking the button update the site map, wait until the process is complete without leaving the page, otherwise, the process will fail!
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="inline-alertbox-wrapper">
                            <div class="inline-alertbox info">
                                <div class="icon">
                                    <?php echo cl_ficon("info"); ?>
                                </div>
                                <div class="alert-message">
                                    <p>
                                        A sitemap is created by splitting CML files into small parts, connecting them through one file, which is an index file, this file that you need to specify when registering your site with search engines. (Submitting your sitemap to Google, etc..)
                                    </p>
                                    <hr>
                                    <p>
                                        Here is the url to this file: <a href="<?php echo cl_link('sitemap/sitemap-index.xml'); ?>" target="_blank"><?php echo cl_link('sitemap/sitemap-index.xml'); ?></a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <?php if (not_empty($cl['config']['sitemap_update'])): ?>
                                <h5>
                                    <span>
                                        Last sitemap update
                                    </span>
                                    <time data-an="last-sitemap-date">
                                        (<?php echo date('d F, Y - h:m', $cl['config']['sitemap_update']); ?>)
                                    </time>    
                                </h5>
                            <?php else: ?>
                                <h5>
                                    <span>
                                        Last sitemap update
                                    </span>
                                    <time data-an="last-sitemap-date">(00/00/0000 - 00:00)</time>
                                </h5>
                            <?php endif; ?>
                        </div>
                        <div class="form-group no-mb">
                            <button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                Update sitemap
                            </button>
                        </div>
                        <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo cl_template('cpanel/assets/sitemap/scripts/app_master_script'); ?>