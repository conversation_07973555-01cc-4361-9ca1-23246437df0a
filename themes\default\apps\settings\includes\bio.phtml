<div class="timeline-container">
	<div class="timeline-header" data-el="tl-header">
		<div class="timeline-header__botline">
			<div class="lp">
				<div class="nav-link-holder">
					<a href="<?php echo cl_link("settings/bio"); ?>" data-spa="true">
						<?php echo cl_translate("About you"); ?>
					</a>
				</div>
			</div>
			<div class="cp">
				<a href="<?php echo cl_link("home"); ?>">
					<img src="{%config site_logo%}" alt="Logo">
				</a>
			</div>
			<div class="rp">
				<div class="nav-link-holder">
					<span class="go-back" onclick="SMColibri.go_back();">
						<?php echo cl_ficon('arrow_back'); ?>
					</span>
				</div>
			</div>
		</div>
	</div>
	<div class="profile-settings">
		<div class="profile-settings__content">
			<div class="settings-form">
				<form class="form" id="cl-bio-settings-vue-app" v-on:submit="submit_form($event)">
					<div class="form-group no-mb">
                        <label>
                            <?php echo cl_translate("About you"); ?>

                            <small class="col-grey">({{bio.length}})</small> 
                        </label>
                        <textarea v-model.trim="$v.bio.$model" name="bio" class="form-control" placeholder="<?php echo cl_translate("Please enter here about yourself"); ?>" rows="3">{{bio}}</textarea>
                        <div class="invalid-main-feedback" v-if="is_valid_bio">
                            {{invalid_feedback_bio}}
                        </div>
                    </div>
                    <div class="form-group" v-if="unsuccessful_attempt">
                        <div class="invalid-main-feedback">
                            <?php echo cl_translate("Something went wrong while trying to save your changes, please try again later"); ?>
                        </div>
                    </div>
                    <div class="form-group" v-else>
                        <div class="form-info-label">
                            <?php echo cl_translate("Please enter a brief description of yourself with a maximum of 140 characters"); ?>
                        </div>
                    </div>
                    <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
					<div class="form-group no-mb d-flex">
						<button v-if="submitting != true" v-bind:disabled="$v.$invalid == true" type="submit" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Save changes"); ?>
	                    </button>
	                    <button v-else disabled="true" type="button" class="ml-auto btn btn-custom main-inline lg">
	                        <?php echo cl_translate("Please wait"); ?>
	                    </button>
					</div>
				</form>
			</div>
		</div>
	</div>

	<script>
		"use strict";

		$(document).ready(function($) {
			Vue.use(window.vuelidate.default);
			var VueValids = window.validators;

			new Vue({
				el: "#cl-bio-settings-vue-app",
				data: {
					submitting: false,
					unsuccessful_attempt: false,
					invalid_feedback_bio: "",
					bio: "<?php echo addslashes($me["about"]) ?>",
				},
				computed: {
					is_valid_bio: function() {
						if (this.$v.bio.$error) {
							this.invalid_feedback_bio = "<?php echo cl_translate("The text you entered is too long, the maximum length is 140 characters"); ?>";
							return true;
						}

						else {
							this.invalid_feedback_bio = "";
							return false;
						}
					}
				},
				validations: {
					bio: {
						max_length: VueValids.maxLength(140)
					}
				},
				methods: {
					submit_form: function(_self = null) {
						_self.preventDefault();

						var _app_ = this;

						$(_self.target).ajaxSubmit({
							url: "<?php echo cl_link("native_api/settings/save_profile_bio"); ?>",
							type: 'POST',
							dataType: 'json',
							beforeSend: function() {
								_app_.submitting = true;
								_app_.unsuccessful_attempt = false;
							},
							success: function(data) {
								if (data.status == 200) {
									cl_bs_notify("<?php echo cl_translate("Your changes has been successfully saved!"); ?>", 5000, "success");
								}

								else {
									_app_.unsuccessful_attempt = true;
								}
							},
							complete: function() {
								_app_.submitting = false;
							}
						});
					}
				}
			});
		});
	</script>
</div>