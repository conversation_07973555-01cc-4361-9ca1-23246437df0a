<tr data-list-item="<?php echo($cl['li']['id']); ?>">
    <td scope="row">
        <?php echo($cl['li']['id']); ?>
    </td>
    <td>
    	<?php if ($cl['li']['type'] == 'image'): ?>
    		<span class="icon">
    			Image
    		</span>
    	<?php elseif($cl['li']['type'] == 'video'): ?>
			<span class="icon">
    			Video
    		</span>
    	<?php elseif($cl['li']['type'] == 'gif'): ?>
			<span class="icon">
    			Gif
    		</span>
    	<?php else: ?>
    		<span class="icon">
    			Text
    		</span>
    	<?php endif; ?>
    </td>
    <td>
    	<?php if ($cl['li']['target'] == 'publication'): ?>
    		Publication
		<?php else: ?>
			Reply
		<?php endif; ?>
    </td>
    <td>
    	<b class="num">
    		<?php echo($cl['li']['replys_count']); ?>
    	</b>
    </td>
    <td>
    	<b class="num">
    		<?php echo($cl['li']['likes_count']); ?>
    	</b>
    </td>
    <td>
    	<b class="num">
    		<?php echo($cl['li']['reposts_count']); ?>
    	</b>
    </td>
    <td>
    	<time>
    		<?php echo($cl['li']['time']); ?>
    	</time>
    </td>
    <td>
    	<div class="dropdown">
            <a href="javascript:void(0);" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                <?php echo cl_ficon("chevron_down"); ?>
            </a>
            <div class="dropdown-menu dropdown-menu-right">
                <a href="<?php echo($cl['li']['url']); ?>" class="dropdown-item">
                    Show thread
                </a>
                <a href="javascript:void(0);" class="dropdown-item" onclick="SMC_CPanel.delete_post('<?php echo($cl['li']['id']); ?>');">
                    Delete
                </a>
            </ul>
        </div>
    </td>
</tr>