<div class="cp-app-container" data-app="premium-account">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                Premium account settings
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        Manage premium account system settings
                    </h2>
                </div>
                <div class="body">
                    <form class="form" data-an="form">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group" data-an="premium_account_mprice-input">
                                    <label>
                                        Premium account monthly price (<b><?php echo strtoupper($cl["config"]["site_currency"]); ?></b>)
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config premium_account_mprice%}" name="premium_account_mprice" type="text" class="form-control" placeholder="PremiE.g. 10">
                                    </div>
                                    <small class="info-feedback">
                                        Payment for a subscription to a premium account will be deducted from the user's wallet
                                    </small>
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <div class="form-group" data-an="prem_account_system_status-input">
                                    <label>
                                        Premium account system status
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="prem_account_system_status" class="form-control">
                                            <option value="on" <?php if($cl['config']['prem_account_system_status'] == 'on') { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl['config']['prem_account_system_status'] == 'off') { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>  
                                    <small class="info-feedback">
                                        <b>IMPORTANT:</b> If you disable this feature, users will not be able to purchase or renew a premium account subscription, but any current subscriptions that were previously purchased will remain active until they expire
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 no-mb">
                                <button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                    Save changes
                                </button>
                            </div>
                        </div>
                        <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php echo cl_template('cpanel/assets/premium_account/scripts/app_master_script'); ?>