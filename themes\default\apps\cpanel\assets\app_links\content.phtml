<div class="cp-app-container" data-app="app-links">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                Mobile apps
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        Managing links to mobile apps
                    </h2>
                </div>
                <div class="body">
                    <form class="form" data-an="form">
                    	<div class="row">
                            <div class="col-lg-6">
                                <div class="form-group" data-an="android_app_url-input">
                                    <label>
                                        Android app (Google play item URL)
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config android_app_url%}" name="android_app_url" type="url" class="form-control" placeholder="Firebase App download URL">
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group" data-an="ios_app_url-input">
                                    <label>
                                        IOS app (App store item URL)
                                    </label>
                                    <div class="form-line">
                                        <input value="{%config ios_app_url%}" name="ios_app_url" type="url" class="form-control" placeholder="Firebase App download URL">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group no-mb">
                       		<button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                Save changes
                            </button>
                        </div>
                        <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo cl_template('cpanel/assets/app_links/scripts/app_master_script'); ?>