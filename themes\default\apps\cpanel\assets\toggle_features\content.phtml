<div class="cp-app-container" data-app="toggle-features">
    <div class="current-page-name">
        <div class="lp">
            <h1>
                Toggle functions (New)
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card">
                <div class="header">
                    <h2>
                        Toggle functions <b>(Enable / Disable)</b>
                    </h2>
                </div>
                <div class="body">
                    <div class="inline-alertbox-wrapper">
                        <div class="inline-alertbox info">
                            <div class="icon">
                                <?php echo cl_ficon("info"); ?>
                            </div>
                            <div class="alert-message">
                                <p>
                                    Please note that after turning off a specific function, users will not be able to use it until you turn them back on.
                                </p>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <h4>
                        System feature switches
                    </h4>
                    <br>
                    <form class="form" data-an="form">
                        <div class="row">
                            <div class="col-xl-6">
                                <div class="form-group" data-an="user_signup-input">
                                    <label>
                                        User registration system
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="user_signup" class="form-control">
                                            <option value="on" <?php if($cl['config']['user_signup'] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl['config']['user_signup'] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        If you disable this option, then new users will not be able to register on your website. <br>
                                        However, you can still invite users through the invite link. <br>

                                        All users who have an invite link will be able to register on your site: <a href="<?php echo cl_link('admin_panel/invite_users'); ?>"><b>Invite users to <?php echo($cl['config']['name']); ?></b></a>
                                    </small>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="form-group" data-an="cookie_warning_popup-input">
                                    <label>
                                        Cookie warning popup
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="cookie_warning_popup" class="form-control">
                                            <option value="on" <?php if($cl['config']['cookie_warning_popup'] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl['config']['cookie_warning_popup'] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        If you disable this option, the user will not be notified about the use of cookies when they enter your site. We strongly recommend that you leave this option enabled in order to avoid problems with the law.
                                    </small>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="form-group" data-an="site_status-input">
                                    <label>
                                        <span style="color: #dc3545; font-weight: bold;">⚠️ Site Maintenance Mode</span>
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="site_status" class="form-control">
                                            <option value="on" <?php if(isset($cl['config']['site_status']) && $cl['config']['site_status'] == "on") { echo('selected'); } elseif(!isset($cl['config']['site_status'])) { echo('selected'); } ?>>
                                                Site Live (Normal Operation)
                                            </option>
                                            <option value="maintenance" <?php if(isset($cl['config']['site_status']) && $cl['config']['site_status'] == "maintenance") { echo('selected'); } ?>>
                                                Maintenance Mode (Site Offline)
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        <b class="col-red">CRITICAL SETTING!</b>
                                        <span class="d-block">
                                            When maintenance mode is enabled, all users will see a maintenance page and cannot access the site. Only administrators can access the site during maintenance mode. Use this when performing updates, maintenance, or troubleshooting.
                                        </span>
                                        <?php if(isset($cl['config']['site_status']) && $cl['config']['site_status'] == "maintenance"): ?>
                                            <span class="d-block" style="color: #dc3545; font-weight: bold; margin-top: 10px;">
                                                🚨 MAINTENANCE MODE IS CURRENTLY ACTIVE! Regular users cannot access the site.
                                            </span>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-xl-6">
                                <div class="form-group" data-an="email_notifications-input">
                                    <label>
                                        E-Mail Notifications system
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="email_notifications" class="form-control">
                                            <option value="on" <?php if($cl["config"]["email_notifications"] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl["config"]["email_notifications"] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        <b class="col-red">IMPORTANT!</b>
                                        <span class="d-block">
                                            Please note that if you enable this feature, then be prepared for a heavy load on the server, and also make sure that your SMTP server is configured and working. SMTP setup can be done on this page
                                        </span>
                                        <span class="d-block">
                                            Here you can setup your SMTP server: <a href="<?php cl_link('admin_panel/email_settings'); ?>">SMTP settings</a>
                                        </span>
                                    </small>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="form-group" data-an="swift_system_status-input">
                                    <label>
                                        Swifts system status (Daily stories)
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="swift_system_status" class="form-control">
                                            <option value="on" <?php if($cl["config"]["swift_system_status"] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl["config"]["swift_system_status"] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        If you turn off this system, then users will not be able to publish stories and also all user stories, including current ones, will be completely hidden for all users.
                                    </small>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-xl-6">
                                <div class="form-group" data-an="system_api_status-input">
                                    <label>
                                        System API status
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="system_api_status" class="form-control">
                                            <option value="on" <?php if($cl["config"]["system_api_status"] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl["config"]["system_api_status"] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        <b class="col-red">IMPORTANT!</b>
                                        <span class="d-block">
                                            Please note that if you disable this feature, then all devices that use the API of this site will be disconnected and will not be able to work in any mode.
                                        </span>
                                    </small>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="form-group" data-an="user_wallet_status-input">
                                    <label>
                                        User wallet status
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="user_wallet_status" class="form-control">
                                            <option value="on" <?php if($cl["config"]["user_wallet_status"] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl["config"]["user_wallet_status"] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        <b class="col-red">IMPORTANT!</b>
                                        <span class="d-block">
                                            Disabling the user wallet system means that your users will not be able to top up their balance, see their wallet status, or pay for subscriptions or other payment transactions on your site
                                        </span>
                                    </small>
                                </div>
                            </div>
                            
                            <div class="form-group no-mb">
                                <button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                    Save changes
                                </button>
                            </div>
                        </div>
                        <hr>
                        <h4>
                            Publication feature switches
                        </h4>
                        <br>
                        <div class="row">
                            <div class="col-xl-6">
                                <div class="form-group" data-an="post_video_download_system-input">
                                    <label>
                                        Post video download system (Allow video download)
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="post_video_download_system" class="form-control">
                                            <option value="on" <?php if($cl["config"]["post_video_download_system"] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl["config"]["post_video_download_system"] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        Please note that if you disable this feature, users will not be able to download videos. In other words, the option to download the video will not appear in the publish drop-down menu.
                                    </small>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="form-group" data-an="post_audio_download_system-input">
                                    <label>
                                        Post audio download system (Allow audio download)
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="post_audio_download_system" class="form-control">
                                            <option value="on" <?php if($cl["config"]["post_audio_download_system"] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl["config"]["post_audio_download_system"] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        Please note that if you disable this feature, users will not be able to download audio. In other words, the option to download the audio will not appear in the publish drop-down menu.
                                    </small>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-xl-6">
                                <div class="form-group" data-an="donation_system_status-input">
                                    <label>
                                        Donation system status (Allows users to raise funds)
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="donation_system_status" class="form-control">
                                            <option value="on" <?php if($cl["config"]["donation_system_status"] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl["config"]["donation_system_status"] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        Please note that if you disable this feature, users will not be able to create fundraisers. However, all current fundraisers will not be affected or stopped
                                    </small>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="form-group" data-an="post_images_system-input">
                                    <label>
                                        Post images system status (Allows users to upload images)
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="post_images_system" class="form-control">
                                            <option value="on" <?php if($cl["config"]["post_images_system"] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl["config"]["post_images_system"] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        Please note that if you disable this feature, users will not be able to upload images to their posts or replies
                                    </small>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="form-group" data-an="post_videos_system-input">
                                    <label>
                                        Post videos system status (Allows users to upload videos)
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="post_videos_system" class="form-control">
                                            <option value="on" <?php if($cl["config"]["post_videos_system"] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl["config"]["post_videos_system"] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        Please note that if you disable this feature, users will not be able to upload videos to their posts or replies
                                    </small>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="form-group" data-an="post_audio_system-input">
                                    <label>
                                        Post audio files system status (Allows users to upload audio files)
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="post_audio_system" class="form-control">
                                            <option value="on" <?php if($cl["config"]["post_audio_system"] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl["config"]["post_audio_system"] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        Please note that if you disable this feature, users will not be able to upload audio files to their posts or replies
                                    </small>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="form-group" data-an="post_documents_system-input">
                                    <label>
                                        Post document files system status (Allows users to upload document files)
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="post_documents_system" class="form-control">
                                            <option value="on" <?php if($cl["config"]["post_documents_system"] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl["config"]["post_documents_system"] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        Please note that if you disable this feature, users will not be able to upload document files to their posts or replies
                                    </small>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="form-group" data-an="post_record_system-input">
                                    <label>
                                        Post voice-recording system status (Allows users to post voice records)
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="post_record_system" class="form-control">
                                            <option value="on" <?php if($cl["config"]["post_record_system"] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl["config"]["post_record_system"] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        Please note that if you disable this feature, users will not be able to post voice records in their publications or replies
                                    </small>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="form-group" data-an="post_polls_system-input">
                                    <label>
                                        Post polls system status (Allows users to create polls)
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="post_polls_system" class="form-control">
                                            <option value="on" <?php if($cl["config"]["post_polls_system"] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl["config"]["post_polls_system"] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        Please note that if you disable this feature, users will not be able to create polls in their publications or replies
                                    </small>
                                </div>
                            </div>
                            <div class="col-xl-6">
                                <div class="form-group" data-an="post_gifs_system-input">
                                    <label>
                                        Post GIFs system status (Allows users to import GIFs)
                                    </label>
                                    <div class="form-line form-select">
                                        <select name="post_gifs_system" class="form-control">
                                            <option value="on" <?php if($cl["config"]["post_gifs_system"] == "on") { echo('selected'); } ?>>
                                                Enabled
                                            </option>
                                            <option value="off" <?php if($cl["config"]["post_gifs_system"] == "off") { echo('selected'); } ?>>
                                                Disabled
                                            </option>
                                        </select>
                                    </div>
                                    <small class="info-feedback">
                                        Please note that if you disable this feature, users will not be able to import GIFs in their publications or replies
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="form-group no-mb">
                       		<button data-an="submit-ctrl" type="submit" class="btn btn-primary">
                                Save changes
                            </button>
                        </div>
                        <input type="hidden" class="d-none" value="<?php echo fetch_or_get($cl['csrf_token'],'none'); ?>" name="hash">
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo cl_template('cpanel/assets/toggle_features/scripts/app_master_script'); ?>